/* do not remove or change this comment as this indicates the top of the file for the spotless licenseHeaderFile
 *
 * This file was generated by the Gradle 'init' task.
 */

plugins {
    // Apply the application-common convention plugin for shared build configuration between application projects.
    id("buildlogic.kotlin-application-common-conventions")
    // Apply the common convention plugin for shared build configuration between library and application projects.
    id("buildlogic.kotlin-common-conventions")

    // Apply the application plugin to add support for building a CLI application in Java.
    application
}

application {
    val isDevelopment: Boolean = project.ext.has("development")
    applicationDefaultJvmArgs = listOf("-Dio.ktor.development=$isDevelopment")
}

dependencies {
}