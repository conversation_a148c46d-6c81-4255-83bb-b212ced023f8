/*
 * SPDX-FileCopyrightText: 2024 <PERSON> <<EMAIL>>
 *
 * SPDX-License-Identifier: Apache-2.0
 */

package com.mwam.kafkakewl.metrics

import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.common.serialization.StringDeserializer
import java.time.Duration
import kotlin.system.exitProcess

/**
 * Test Kafka Consumer application for simulating consumer lag scenarios.
 * 
 * This application consumes messages from a Kafka topic with a specified consumer group
 * and provides interactive options for each message to simulate different consumer behaviors.
 * 
 * Usage:
 * ./gradlew :kafkakewl-metrics:test --tests TestKafkaConsumer
 * 
 * Or run directly with arguments:
 * java -cp ... com.mwam.kafkakewl.metrics.TestKafkaConsumerKt [brokers] [topic] [consumerGroup]
 * 
 * Default values:
 * - brokers: localhost:9092
 * - topic: kewl.persistent-store
 * - consumerGroup: kewl.test-consumer-group
 */
fun main(args: Array<String>) {
    val brokers = args.getOrNull(0) ?: "localhost:9092"
    val topic = args.getOrNull(1) ?: "kewl.persistent-store"
    val consumerGroup = args.getOrNull(2) ?: "kewl.test-consumer-group"
    
    println("=== Test Kafka Consumer ===")
    println("Brokers: $brokers")
    println("Topic: $topic")
    println("Consumer Group: $consumerGroup")
    println("=============================")
    println()
    
    val consumer = createKafkaConsumer(brokers, consumerGroup)
    
    try {
        consumer.subscribe(listOf(topic))
        println("Subscribed to topic: $topic")
        println("Waiting for messages... (Press Ctrl+C to exit)")
        println()
        
        var messageCount = 0
        
        while (true) {
            val records = consumer.poll(Duration.ofMillis(1000))
            
            if (records.isEmpty) {
                print(".")
                continue
            }
            
            println() // New line after dots
            
            for (record in records) {
                messageCount++
                
                println("--- Message #$messageCount ---")
                println("Partition: ${record.partition()}")
                println("Offset: ${record.offset()}")
                println("Key: ${record.key() ?: "<null>"}")
                println("Value: ${record.value() ?: "<null>"}")
                println("Timestamp: ${record.timestamp()}")
                println("Headers: ${record.headers().map { "${it.key()}=${String(it.value())}" }}")
                println()
                
                val action = promptUserAction()
                
                when (action) {
                    UserAction.COMMIT_AND_CONTINUE -> {
                        consumer.commitSync()
                        println("✓ Offset committed")
                        println()
                    }
                    UserAction.CONTINUE_WITHOUT_COMMIT -> {
                        println("→ Continuing without commit")
                        println()
                    }
                    UserAction.STOP -> {
                        println("Stopping consumer...")
                        return
                    }
                }
            }
        }
    } catch (e: Exception) {
        println("Error occurred: ${e.message}")
        e.printStackTrace()
    } finally {
        consumer.close()
        println("Consumer closed.")
    }
}

private fun createKafkaConsumer(brokers: String, consumerGroup: String): KafkaConsumer<String, String> {
    val props = mapOf(
        ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to brokers,
        ConsumerConfig.GROUP_ID_CONFIG to consumerGroup,
        ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG to StringDeserializer::class.java.name,
        ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG to StringDeserializer::class.java.name,
        ConsumerConfig.AUTO_OFFSET_RESET_CONFIG to "earliest",
        ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG to "false", // Manual commit control
        ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG to "30000",
        ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG to "10000"
    )
    
    return KafkaConsumer<String, String>(props)
}

private enum class UserAction {
    COMMIT_AND_CONTINUE,
    CONTINUE_WITHOUT_COMMIT,
    STOP
}

private fun promptUserAction(): UserAction {
    while (true) {
        println("Choose action:")
        println("  [c] Commit offset and continue")
        println("  [n] Continue without commit")
        println("  [s] Stop consumer")
        print("Enter choice (c/n/s): ")
        
        val input = readlnOrNull()?.lowercase()?.trim()
        
        return when (input) {
            "c", "commit" -> UserAction.COMMIT_AND_CONTINUE
            "n", "no", "continue" -> UserAction.CONTINUE_WITHOUT_COMMIT
            "s", "stop", "exit" -> UserAction.STOP
            else -> {
                println("Invalid choice. Please enter 'c', 'n', or 's'.")
                continue
            }
        }
    }
}
